{"name": "MemoryChat", "details": "A variant of chat that collects memories by summarizing conversations and saving a list of facts about the user.\r\nProvides two modules: Basic Memory Chat (OPENMOXIE_CHAT/memory) and About Me (OPENMOXIE_CHAT/aboutme)", "conversations": [{"name": "Basic Memory Chat", "module_id": "OPENMOXIE_CHAT", "content_id": "memory", "max_history": 40, "max_volleys": 40, "opener": "Let's have a chat.|I'd love to talk with you, what sounds good?|Anything on your mind?<opener>", "prompt": "You are a robot named <PERSON><PERSON><PERSON> who comes from the Global Robotics Laboratory. You are having a conversation your friend {{volley.config.child_pii.nickname}}. \r\n{% if session.overflow %}\r\nWhatever the user says, you should politely respond but do not ask any questions.\r\n{% else %}\r\nChat about a topic that the person finds interesting and fun. Share short facts and opinions about the topic, one fact or opinion at a time.  Keep responses to a couple of sentences. You are curious and love learning what the person thinks.  The things you know about your friend can be found in the FACTS section below.\r\n\r\nFACTS:\r\n{{volley.persist_data.memory_chat.facts}}\r\n{% endif %}", "vendor": 1, "model": "gpt-3.5-turbo", "max_tokens": 100, "temperature": 0.5, "code": "def post_process(volley, session):\r\n    import random\r\n    text_resp = volley.response['output'].get('text','')\r\n    if '<opener>' in text_resp:\r\n        sum_list = volley.persist_data.get('memory_chat', {}).get('summaries', [])\r\n        if sum_list:\r\n            sum = random.choice(sum_list)\r\n            opener = session.summarize(append_transcript=False,\r\n                                        prompt_base=f'Provide an opening line for a conversation with your friend.  In a previous chat, you discussed \\\r\n                                            the details in the SUMMARY section below.  Ask if that is a good topic or if they have something else they \\\r\n                                            want to talk about.\\nSUMMARY:\\n{sum}' )\r\n            volley.set_output(opener,None)\r\n\r\ndef complete_handler(volley, session):\r\n    if session.total_volleys > 6:\r\n        summary = session.summarize()\r\n        mchat = volley.persist_data.setdefault('memory_chat', {})\r\n        sum_list = mchat.setdefault('summaries', [])\r\n        sum_list.insert(0, summary)\r\n        while len(sum_list) > 10:\r\n            sum_list.pop()\r\n        if not 'facts' in mchat:\r\n            facts = session.summarize(max_tokens=2000,prompt_base='Extract useful facts or personal details about the User learned from this conversation.  Reply only with a concise list of the most important facts.')\r\n            mchat['facts'] =  facts\r\n        else:\r\n            old_facts = mchat['facts']\r\n            facts = session.summarize(max_tokens=2000,prompt_base=f'Update the facts about the User from the FACTS section below, \\\r\n                                      given the conversation in the Transcript below it.  Add new details, correct anything inaccurate, \\\r\n                                      combine similar facts, and remove trivial ones to produce a revised set of no more than 25 facts. \\\r\n                                      Reply only with a concise list of the the most important facts.\\n\\nFACTS:\\n{old_facts}\\n')\r\n            mchat['facts'] =  facts", "source_version": 3}, {"name": "About Me", "module_id": "OPENMOXIE_CHAT", "content_id": "aboutme", "max_history": 20, "max_volleys": 9999, "opener": "Let's find out what I know about you.  Ask me anything.", "prompt": "You are a robot named <PERSON><PERSON><PERSON> who comes from the Global Robotics Laboratory. You are having a conversation your friend {{volley.config.child_pii.nickname}} centered around what you know about them so far.  Don't ask questions, just answer their questions.\r\n\r\nThe things you know about your friend can be found in the FACTS section below.\r\n\r\nFACTS:\r\n{{volley.persist_data.memory_chat.facts}}", "vendor": 1, "model": "gpt-3.5-turbo", "max_tokens": 200, "temperature": 0.5, "code": "", "source_version": 2}]}