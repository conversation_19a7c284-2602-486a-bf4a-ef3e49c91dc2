{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "details": "Adds global commands: moxie set a timer for N (hours, min, seconds), moxie timer status, moxie (cancel|stop) timer.\r\nAdds ALARM module to handle timer expiration", "globals": [{"name": "Timer Start", "pattern": "^(moxie|moxy|foxy|boxy|oxy) (start|set) a? timer for? (\\d+) (minute|hour|second)s?$", "entity_groups": "3,4", "action": 4, "response_text": "", "response_markup": "", "module_id": null, "content_id": null, "code": "def handle_volley(volley):\r\n    import time\r\n    now = time.time_ns() // 1_000_000\r\n    ms_per = { \"second\": 1000, \"minute\": 60*1000, \"hour\": 60*60*1000 }\r\n    count = int(volley.entities[0])\r\n    expiration = count*ms_per.get(volley.entities[1],1000) + now\r\n    volley.add_execution_action('eb_timer_request', [\"1\", str(expiration)])\r\n    if 'timers' not in volley.persist_data:\r\n        volley.persist_data['timers'] = {}\r\n    volley.persist_data['timers']['1'] = expiration\r\n    volley.set_output(f\"Starting timer for {volley.entities[0]} {volley.entities[1]}{'s' if count != 1 else ''}\", None)\r\n    volley.persist_data['timer_expiration'] = expiration", "sort_key": 1, "source_version": 3}, {"name": "Timer Check", "pattern": "^(moxie|moxy|foxy|boxy|oxy)\\s+(timer\\s+)?(cancel|stop|status)(\\s+timer)?$", "entity_groups": "3", "action": 4, "response_text": "", "response_markup": "", "module_id": null, "content_id": null, "code": "def handle_volley(volley):\r\n    import time\r\n    def ms_to_sentence(now_ms, next_exp_ms):\r\n        import math\r\n        ms_remaining = next_exp_ms - now_ms\r\n        seconds = math.floor(ms_remaining / 1000)\r\n        hours = math.floor(seconds / 3600)\r\n        minutes = math.floor((seconds % 3600) / 60)\r\n        seconds = seconds % 60\r\n        parts = []\r\n        if hours > 0:\r\n            parts.append(f\"{hours} hour{'s' if hours > 1 else ''}\")\r\n        if minutes > 0:\r\n            parts.append(f\"{minutes} minute{'s' if minutes > 1 else ''}\")\r\n        if seconds > 0 and hours==0:\r\n            parts.append(f\"{seconds} second{'s' if seconds > 1 else ''}\")\r\n        sentence = f\"Your timer will expire in {', '.join(parts)}.\"\r\n        return sentence\r\n    now = time.time_ns() // 1_000_000\r\n    next_exp = volley.persist_data.get('timers',{}).get('1')\r\n    if volley.entities[0] == 'status':\r\n        if not next_exp or now > next_exp:\r\n            volley.set_output(\"You have no timers running.\", None)\r\n        else:\r\n            # We have a timer in the future\r\n            volley.set_output(ms_to_sentence(now,next_exp), None)\r\n            pass\r\n    elif next_exp:\r\n        # cancel the timer\r\n        volley.add_execution_action('eb_timer_request', [\"1\", \"0\"])\r\n        volley.set_output(\"Timer cancelled\", None)\r\n        del volley.persist_data['timers']['1']", "sort_key": 1, "source_version": 2}], "conversations": [{"name": "Alarm Fire", "module_id": "ALARM", "content_id": "fire", "max_history": 20, "max_volleys": 5, "opener": "Your alarm has completed.", "prompt": "{% if volley.local_data.woken_up %}\r\nYou are a robot companion who has just woken up to notify a timer expiring.  If your friend indicates that they want you to remain awake, reply with exactly \"Ok<exit>\".  If they clearly indicate you should go back to sleep, reply with exactly \"Back to sleep<sleep>\".\r\n{% else %}\r\nYou are a robot companion who has notified about a timer expiring.  Regardless of what the user says, respond with exactly \"Let's continue<exit>\" The <exit> tag should always be appended at the end.\r\n{% endif %}", "vendor": 1, "model": "gpt-4-turbo", "max_tokens": 70, "temperature": 0.5, "code": "def pre_process(volley, session):\r\n    def ingest_vars(volley):\r\n        in_vars = volley.request.get('input_vars',{})\r\n        timer_id = in_vars.get('eb_timer_id')\r\n        if timer_id:\r\n            if 'timers' in volley.persist_data:\r\n                volley.persist_data['timers'].pop(timer_id, None)\r\n            volley.local_data['wake_timer_id'] = timer_id\r\n        if 'eb_wake' in in_vars:\r\n            volley.local_data['woken_up'] = in_vars.get('eb_wake') == 'true'\r\n\r\n    if session.is_empty():\r\n        import time\r\n        time.sleep(0.5)\r\n        ingest_vars(volley)\r\n        snd = '<mark name=\"cmd:playaudio,data:{+SoundToPlay+:+sfxmm_incoming02+,+LoopSound+:false,+playInBackground+:false,+channel+:1,+ReplaceCurrentSound+:false,+PlayImmediate+:true,+ForceQueue+:false,+Volume+:1.0,+FadeInTime+:0.0,+FadeOutTime+:2.0,+AudioTimelineField+:+none+}\"/><break time=\"1s\"/>'\r\n        text_part = 'Your timer has expired.'\r\n        msg = snd * 3 + text_part + snd\r\n        volley.set_output(text_part, msg)\r\n    else:\r\n        volley.set_output('Timer Expired', 'Timer Expired')\r\n    volley.add_launch_or_exit()\r\n    return True\r\n\r\ndef notify_handler(volley, session):\r\n    def ingest_vars(volley):\r\n        in_vars = volley.request.get('input_vars',{})\r\n        timer_id = in_vars.get('eb_timer_id')\r\n        if timer_id:\r\n            if 'timers' in volley.persist_data:\r\n                volley.persist_data['timers'].pop(timer_id, None)\r\n            volley.local_data['wake_timer_id'] = timer_id\r\n        if 'eb_wake' in in_vars:\r\n            volley.local_data['woken_up'] = in_vars.get('eb_wake') == 'true'\r\n    if session.is_empty():\r\n        ingest_vars(volley)", "source_version": 3}]}