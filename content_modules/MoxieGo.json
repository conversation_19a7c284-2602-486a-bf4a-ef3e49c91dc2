{"name": "MoxieGo", "details": "Moxie Go - 'moxie go' triggers activity selection, can show launch action QR codes or ask for things.\r\nIncludes moxie_go_hub_timers schedule, which uses Moxie Go as a hub module and supports alarm configuration if using the MoxieTimers module.", "globals": [{"name": "<PERSON><PERSON><PERSON>er", "pattern": "^(moxie|moxy|foxy|boxy|oxy) go$", "entity_groups": null, "action": 2, "response_text": "Let's go!", "response_markup": "", "module_id": "MOXIE_GO", "content_id": "default", "code": "", "sort_key": 1, "source_version": 2}], "schedules": [{"name": "moxie_go_hub_timers", "schedule": {"provided_schedule": [{"module_id": "ENROLLCONVO"}, {"module_id": "EVENTSANDHOLIDAYS"}, {"module_id": "OPENMOXIE_CHAT", "content_id": "short"}, {"module_id": "DM"}], "generate": {"chat_count": 2, "module_count": 6, "chat_modules": [{"module_id": "OPENMOXIE_CHAT", "content_id": "short"}], "extra_modules": [], "excluded_module_ids": []}, "chat_request": {"module_id": "OPENMOXIE_CHAT", "content_id": "default"}, "alarm_module": {"module_id": "ALARM", "content_id": "fire"}, "hub_config": {"hubs": [{"module_id": "MOXIE_GO", "content_id": "default"}]}}, "source_version": 2}], "conversations": [{"name": "Moxie GO", "module_id": "MOXIE_GO", "content_id": "default", "max_history": 20, "max_volleys": 9999, "opener": "Let's pick something to do.  You can show me a card any time.", "prompt": "You are a robot from the GRL trying to help your mentor find something to do.  They can always hold up a card that shows you which activity they want,\r\nbut you can also help them select one from the list of modules in MODULE_LIST below.   Try to help them find an activity.  When you are sure you know what they want, confirm it with them, then append '<launch:XXX>' to your response to them, where XXX is replaced by the label.  For example '<launch:AB>' to launch the Animal Breathing Exercise.\r\n\r\nKeep your responses short, limited to a couple of sentences, but give choices when they make sense.\r\n\r\nMODULE_LIST\r\n\r\nAFFIRM - Positive affirmations for self-regulation\r\nAB - Animal breathing exercise for self-regulation\r\nANIMALEXERCISE - Movement exercise inspired by animals\r\nBODYSCAN - Body scan for relaxation and self-regulation\r\nRDL - Riddles\r\nBREATHINGSHAPES - Breathing exercises for self-regulation\r\nCOMPOSING - Composing music for creativity\r\nFACES - Guess the facial expression game\r\nFF - Fun fact of the day\r\nGUIDEDVIS - Guided visualization for relaxation\r\nJOKE - Daily joke for laughter\r\nJUKEBOX - Music listening exercise\r\nMENTORSAYS - <PERSON> says game\r\nDANCE - Dance movement exercise\r\nDRAW - Drawing exercise for creativity\r\nSTORYTELLING - Tell your own story\r\nPASSWORDGAME - Password guessing game for problem-solving\r\nREAD - Read a book with <PERSON><PERSON><PERSON>AVENGERHUNT - Scavenger hunt game for playful learning\r\nSTORY - Listen to a story from <PERSON><PERSON><PERSON>\r\nAUDMED - Audio meditation for relaxation\r\nDM - Learning Missions", "vendor": 1, "model": "gpt-4o", "max_tokens": 100, "temperature": 0.5, "code": "def pre_process(volley, session):\r\n    in_speech = volley.request.get(\"speech\")\r\n    if not in_speech:\r\n        volley.add_execution_action('eb_enable_qr', ['true'])\r\n        volley.update_subscriptions(['eb-qr-event'])\r\n    if in_speech == 'eb-qr-event':\r\n        qr_data = volley.request.get(\"input_vars\", {}).get(\"$eb_qr_value\",\"Error\")\r\n        if qr_data.startswith('GO'):\r\n            volley.set_output(f\"You got it!{qr_data[2:]}\", None)\r\n        else:\r\n            volley.set_output(\"I saw a code I don't know.  Let's try again.\", None)\r\n            volley.add_execution_action('eb_enable_qr', ['true'])\r\n        return True\r\n    return False\r\n\r\ndef complete_handler(volley, session):\r\n    summary = session.summarize()\r\n    volley.persist_data['last_summary'] = summary\r\n    print(f\"Summary: {summary}\")", "source_version": 3}]}