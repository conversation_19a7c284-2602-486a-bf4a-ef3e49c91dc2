{"name": "MoxieTime", "details": "<PERSON><PERSON> \"Mo<PERSON>e what time is it\"", "globals": [{"name": "Time Command", "pattern": "^(moxie|moxy|foxy|boxy|oxy) (time|what(?:'s| is) the time|what time is it)$", "entity_groups": null, "action": 4, "response_text": "The time is the time, what of it?", "response_markup": "", "module_id": null, "content_id": null, "code": "def get_response(request, response, entities):\r\n    def get_current_time():\r\n        from datetime import datetime\r\n        now = datetime.now()\r\n        am_pm = \"AY M\" if now.hour < 12 else \"P M\" # Moxie speak\r\n        hour = now.hour % 12 or 12  # Convert to 12-hour format\r\n        current_time = f\"The time is {hour}:{now.strftime('%M')} {am_pm}\"\r\n        return current_time\r\n    return get_current_time()", "sort_key": 1, "source_version": 3}]}