# -*- coding: utf-8 -*-
"""Takes the input string and marks it up with voice and gestural tags."""

import logging
import os
import re
import sys
from typing import Union, Tuple, Dict
import xml.etree.ElementTree as ET
from unidecode import unidecode

from .utils import bcolors
from .markup_core import markup_xmlassembly
from .markup_core.tagspan import TagSpan
from .markup_types import markup_behavior
from .markup_types import markup_mood
from .markup_types import markup_voice
from .markup_types.markup_pauses import *
from .ml import mlparams
from .ml import mlrules_utils
from ._version import __package_version__

XML_OUTPUT_COLORIZE_COLOR = bcolors.OKGREEN
ADD_AUTO_GEN_ATTRIB = mlparams.ADD_AUTO_GENERATED_ATTRIB
AUTO_GEN_ATTRIB_NAME = "autogenerated"
REMOVE_SINGLE_WORD_USEL_TAGS = mlparams.REMOVE_SINGLE_WORD_USEL_TAGS
ACCEPTED_SINGLE_WORD_USEL_TAGS = mlparams.ACCEPTED_SINGLE_WORD_USEL_TAGS

# These are chars that are considered "padding"
# (i.e. the replace-string is "afaik", and pad-char is ":" in "Well, afaik: Moxie is great")
PAD_CHARS = " ,.\n\t:;(){}[]-_+="
INTERNAL_REPLACE_STRINGS = {
    "&": " and ",
    ";": "",
    "*": ""
}


def check_span_conflicts(spans_per_tag: Dict[str, List[TagSpan]]
                         ) -> Tuple[Dict[str, List[int]], bool, Tuple[int, str, int]]:
    """
    Check conflicting SCOPEs. Can use RemoveWorstOffendingSpan() after to prune offenders
    ie. <tag1><tag2></tag1></tag2> is considered a conflict; bad nesting

    Returns: spans_per_tag_check (dict), conflict_somewhere (bool)
    """
    debug = False
    spans_per_tag_check: Dict[str, List[int]] = {}
    span_tags = spans_per_tag.keys()
    conflict_somewhere: bool = False

    worst_offender_conflict_count: int = 0
    worst_offender_tag: Union[str, None] = None
    worst_offender_span_index: int = -1

    if debug:
        print(f"Checking {len(span_tags)} span_tags: {span_tags}")
    for tag in span_tags:
        spans_per_tag_check[tag]: List[int] = []
        these_spans: List[TagSpan] = spans_per_tag[tag]

        # Check internally for self-conflict
        if debug:
            print(f"Check internally for self-conflict (starting with {bcolors.OKGREEN}{len(these_spans)}{bcolors.ENDC} spans)")
        i_span = len(these_spans) - 1
        while i_span >= 0:
            if debug:
                print(f"    Checking i_span[{i_span}]")
            i_conflicted = False
            j_span = len(these_spans) - 1
            while j_span >= 0:
                if debug:
                    print(f"        Checking j_span[{j_span}]")
                if these_spans[j_span] is these_spans[i_span]:
                    j_span -= 1
                    continue

                conflicted, msg = these_spans[j_span].conflicts(these_spans[i_span])
                if conflicted:
                    i_conflicted = True
                    if debug:
                        print(f"{bcolors.FAIL if conflicted else bcolors.OKGREEN}{msg}{bcolors.ENDC}")
                    break

                j_span -= 1

            if i_conflicted:
                these_spans.remove(these_spans[i_span])
            i_span -= 1

        these_spans: List[TagSpan] = spans_per_tag[tag]
        if debug:
            print(f"Internal testing for self-conflict results in {bcolors.OKGREEN}{len(these_spans)}{bcolors.ENDC} remaining spans")

        # Check against other tag's spans
        if debug:
            print(f"Check externally against other tag's spans")
        # Initialize with 0 conflicts
        # check_this_tag = True
        i_span = 0
        while i_span < len(these_spans):
            this_span: TagSpan = these_spans[i_span]
            conflict_count = 0

            # e: Union[ET.Element, None] = None
            # if isinstance(this_span, TagSpan):
            #     if debug:
            #         print("    Deserializing TAGSPAN: {}".format(this_span.associated_str))
            #     e = mlrules_utils.deserialize_element(this_span.associated_str)
            # else:
            #     raise Exception("Should never come here?")
            #     # if debug:
            #     #     print("    Deserializing string span: {}".format(this_span))
            #     # e = mlrules_utils.deserialize_element(this_span)

            # if AUTO_GEN_ATTRIB_NAME in e.attrib.keys() and int(e.attrib[AUTO_GEN_ATTRIB_NAME]) >= 1:
            #     check_this_tag = False
            # raise Exception(check_this_tag)

            # if check_this_tag:

            # Now check other tag-spans for conflicts against this span
            for other_tag in span_tags:
                if other_tag == tag:
                    continue

                other_tag_spans = spans_per_tag[other_tag]
                if debug:
                    print(f"Checking if {bcolors.OKBLUE}{tag}{bcolors.ENDC} span[{bcolors.OKBLUE}{i_span}{bcolors.ENDC}] has conflicts in {bcolors.OKBLUE}{other_tag}{bcolors.ENDC} ({len(other_tag_spans)} span(s))")
                for other_span in other_tag_spans:
                    conflicted, msg = this_span.conflicts(other_span)
                    if conflicted:
                        conflict_count += 1
                    if debug:
                        print("[{:2}, {:2}] => [{:2}, {:2}] ({})".format(this_span.start_index,
                                                                         this_span.end_index,
                                                                         other_span.start_index,
                                                                         other_span.end_index,
                                                                         f"{bcolors.FAIL if conflicted else bcolors.OKGREEN}{msg}{bcolors.ENDC}"
                                                                         ))

            if conflict_count > 0:
                conflict_somewhere = True

                # Record worst offender
                if conflict_count > worst_offender_conflict_count:
                    worst_offender_conflict_count = conflict_count
                    worst_offender_tag = tag
                    worst_offender_span_index = i_span

            spans_per_tag_check[tag].append(conflict_count)
            i_span += 1

    return spans_per_tag_check, conflict_somewhere, (worst_offender_conflict_count, worst_offender_tag, worst_offender_span_index)


def remove_worst_offending_span(spansPerTag: Dict[str, List[TagSpan]], worstOffender: Tuple[int, str, int]):
    """Look for worst offenders first using CheckSpanConflicts()"""
    tag = worstOffender[1]
    span_index = worstOffender[2]

    span_list = spansPerTag[tag]
    span_list.remove(span_list[span_index])
    return spansPerTag


def strip(markup_str: str) -> str:
    """
    Strips markup from string and returns the plain text

    NOTE: This is the same code as in chatscript_utils.py from the main bo-android project. Changes here should
    make it back to that repo to avoid duplicated effort/bug-tracking.
    """

    def _basic_test(markup: str, filename: str = ""):
        # Test valid XML tree form
        try:
            cleaned_string = markup.replace("<mentor name>", "")
            ET.fromstring(f"<root>{cleaned_string}</root>")
        except BaseException:
            error_msg = f"\nFile: {filename}\nMarkup String Invalid: '{markup}'"
            raise ValueError(error_msg)

    def _recurse(element: ET.Element, visited_elements: List[ET.Element] = [], result_string: str = ""):
        visited_elements.append(element)

        if element.text != None:
            result_string += f" {element.text}"
        for e_child in element.iter():
            if e_child != element and e_child not in visited_elements:
                result_string = _recurse(e_child, visited_elements=visited_elements, result_string=result_string)
        if element.tail != None:
            result_string += f" {element.tail}"

        return result_string

    _basic_test(markup=markup_str)

    cleaned_string = markup_str.replace("<mentor name>", "")
    root = ET.fromstring(f"<root>{cleaned_string}</root>")
    result_string = _recurse(root)

    # White space cleanup
    result_string = result_string.replace("  ", " ")
    while len(result_string) > 0 and result_string[0] == " ":
        result_string = result_string[1:]

    return result_string


def remove_quotes(markup_str: str) -> str:
    """
    Strips double-quotes from marked up string's text-body
    """
    def _recurse(element: ET.Element, visited_elements: List[ET.Element] = []):
        visited_elements.append(element)
        bad_quotes = '"'

        if element.text is not None and element.text != None and bad_quotes in element.text:
            element.text = element.text.replace(bad_quotes, "")
        for e_child in element.iter():
            if e_child != element and e_child not in visited_elements:
                _recurse(e_child, visited_elements=visited_elements)
        if element.tail is not None and element.tail != None and bad_quotes in element.tail:
            element.tail = element.tail.replace(bad_quotes, "")

    cleaned_string = markup_str.replace("<mentor name>", "")
    root = ET.fromstring(f"<root>{cleaned_string}</root>")
    orig_xml = ET.tostring(root).decode("utf8")
    orig_xml = orig_xml.replace("<root>", "").replace("</root>", "")
    _recurse(root)

    xml_string: str = ET.tostring(root).decode("utf8")
    xml_string = xml_string.replace("<root>", "").replace("</root>", "")
    if xml_string != orig_xml:
        # raise Exception(f"CHANGED!"
        #                 f"\n===================="
        #                 f"\nFROM:"
        #                 f"\n{orig_xml}"
        #                 f"\n===================="
        #                 f"\nTO:"
        #                 f"\n{xml_string}")
        return xml_string
    else:
        return markup_str


def get_internal_text_replacements() -> Dict[str, str]:
    data_path = mlparams.TXT_REPLACE_FILE_PATH
    if not os.path.exists(mlparams.TXT_REPLACE_FILE_PATH):
        data_path = mlparams.TXT_REPLACE_FILE_EXE_PATH

    replacements: Dict[str, str] = {}
    if not os.path.exists(data_path):
        logging.error(f"Text replacement file cannot be found, empty replacement dictionary will be returned: "
                      f"'{data_path}'")
    else:
        logging.info(f"Loading text replacement json: file://{data_path}")
        with open(data_path, "r") as f:
            replacements = json.loads(f.read())
            logging.info(f"    Got {len(replacements.keys())} replacement keys")

    return replacements


def markup_sentence(s: str,
                    rules: dict,
                    markVoice: bool = True,
                    synthRate: float = 1.0,
                    markVoiceSpecialMarkGenre: bool = True,
                    markBehaviors: bool = True,
                    markMoodAndIntensity: Union[Tuple[str, float], None] = None,
                    prettyPrint: bool = True,
                    markup_pauses: float = None,
                    text_replacements: Dict[str, str] = None,
                    lastSentence : bool = False,
                    debug: bool = False):
    
    def replace_string_with_pad(string: str, replace_key: str, replace_value: str) -> str:
        """
        Replace words with preceding and ensuing spaces to ensure the whole word is cleanly replaced.
        """
        if replace_key in string:
            j = 0
            string_len = len(string)  # This is expected to change during replacement, so we make it a variable
            while j < string_len and (j + len(replace_key)) <= string_len:
                text_end = j + len(replace_key)
                line_substring = string[j: text_end]

                pad_front = False
                pad_after = False
                if j == 0 and text_end < string_len:  # Beginning of text
                    pad_after = True
                elif j == (string_len - len(replace_key)):  # EOL
                    pad_front = True
                else:  # Middle of text
                    pad_front = True
                    pad_after = True

                if line_substring == replace_key:
                    do_it = True
                    if pad_front and string[j - 1] not in PAD_CHARS:
                        do_it = False
                    if pad_after and string[text_end] not in PAD_CHARS:
                        do_it = False

                    if do_it:
                        string = string[:j] + replace_value + string[text_end:]
                        string_len = len(string)
                        j += (len(replace_value) - len(replace_key))

                j += 1
        return string
    
    def can_remove_this_tag(span):
        # do nothing if the flag is not set
        if not REMOVE_SINGLE_WORD_USEL_TAGS: return False
        # don't ever remove single-word-tags for certain types of tags
        if 'usel' in span.keys() and 'genre' in span['usel'].keys() and span['usel']['genre'] in ACCEPTED_SINGLE_WORD_USEL_TAGS and \
           'source' in span['usel'].keys() and span['usel']['source'] == 'mark':
            return False
        return True


    # Do text replacement
    # External replacement file
    if text_replacements is not None:
        logging.info(f"Replacing text based on passed in dictionary ({len(text_replacements.keys())} keys): {s}")
        for key in text_replacements.keys():
            s = replace_string_with_pad(string=s, replace_key=key, replace_value=text_replacements[key])

        logging.info(f"    Replacement result: {s}")

    colorizeXmlOutputForEasyDebug = True
    origS = re.sub(r'  ', ' ', s)
    cleanedS = re.sub(r'['+mlparams.SPECIAL_CHARS+']', '', origS)
    origWords = origS.split(' ')
    origWords.append(mlparams.CHAR_EOL)
    words = cleanedS.lower().split(' ')
    words.append(mlparams.CHAR_EOL)

    # Generate a nested list of rules-per-word
    rulesPerWordDict = {}
    if markVoice:
        rulesPerWordDict = markup_voice.markup(words, origWords, rules, markVoiceSpecialMarkGenre=markVoiceSpecialMarkGenre, synthRate=synthRate, debug=debug)

    # Add behavior markups
    if markBehaviors:
        if debug: print("Adding behaviors markup")
        behaviorRules = markup_behavior.markup(words, origWords)
        rulesPerWordDict[markup_behavior.TAG] = behaviorRules

    # Add playback-mood markups
    if markMoodAndIntensity is not None :
        if len(markMoodAndIntensity) != 2:
            if debug: print("{}Cannot add mood markup, invalid data – expecting json string or list with 2 values, got this instead: {}{}".format(bcolors.WARNING, markMoodAndIntensity, bcolors.ENDC))
        else:
            if debug: print("Adding mood markup")
            moodRules = markup_mood.markup(words, mood=markMoodAndIntensity[0], intensity=markMoodAndIntensity[1])
            rulesPerWordDict[markup_mood.TAG] = moodRules

    if not lastSentence:
        rulesPerWordDict[MarkupPauses.TAG] = MarkupPauses.pause_rule(words, pause_seconds=markup_pauses)

    # Print/preview results
    if debug:
        print("{}Rules per word{}".format(bcolors.PURPLE, bcolors.ENDC))
        for tag in rulesPerWordDict.keys():
            i = 0
            while i < len(words):
                print("    tag={:10} word={:15} markup={}".format(tag, words[i], rulesPerWordDict[tag][i]))
                i += 1

    # Insert non-scoped markups now
    markedWords = origWords
    if debug: print("Inserting non-scoped markups")
    for tag in rulesPerWordDict.keys():
        if tag in mlparams.UNSCOPED_TAGS:
            i = 0
            while i < len(words):
                rule = rulesPerWordDict[tag][i]
                if rule is not None:
                    markupE = mlrules_utils.deserialize_element(rule)
                    updatedWord = ET.tostring(markupE).decode("UTF-8") + markedWords[i]
                    if debug: print("    word[{}] = {}".format(i, updatedWord))
                    markedWords[i] = updatedWord
                i += 1

    # Build spans
    if debug: print("Building spans...")
    spansPerTag = { }
    for tag in rulesPerWordDict.keys():
        if tag in mlparams.UNSCOPED_TAGS:
            if debug: print("Skipping unscoped tag '{}' for span-merging".format(tag))
            continue

        spansPerTag[tag] = [ ]
        i = 0
        span = [ ]
        lastRule = None
        while i < len(words):
            thisRule = rulesPerWordDict[tag][i]
            if thisRule is None:
                if len(span) > 0: # lastRule is already not None at this point
                    spansPerTag[tag].append(TagSpan(lastRule, span[0], span[-1]))
                    span = []
                lastRule = None
            else:
                if thisRule == lastRule:
                    span.append(i)
                else:
                    if len(span) > 0:
                        spansPerTag[tag].append(TagSpan(lastRule, span[0], span[-1]))
                    span = [ i ]
                    lastRule = thisRule
            i += 1

    ###################################################################################
    # The Sauce Section ###############################################################
    ###################################################################################
    # Merge tags section - merge tags with the same attributes that are fairly close together (closeness defined in mlparams.MAX_SPAN_MERGE_WORD_SEPARATION)
    # For example:
    # MERGING: {"usel": {"genre": "none", "variant": "2"}} ([27, 27]) => {"usel": {"genre": "none", "variant": "2"}} ([19, 19])
    # Now: {"usel": {"genre": "none", "variant": "2"}} ([19, 27])
    for tag in spansPerTag.keys():
        if tag in mlparams.UNSCOPED_TAGS:
            if debug: print("Skipping unscoped tag '{}' for span-merging".format(tag))
            continue

        iSpan = 0
        while iSpan < len(spansPerTag[tag]):
            span = spansPerTag[tag][iSpan]
            wordSeparationCount = 0
            tagAttrsMatched = True
            iOtherSpan = iSpan + 1
            while iOtherSpan < len(spansPerTag[tag]):
                otherSpan = spansPerTag[tag][iOtherSpan]
                if span.associated_str == otherSpan.associated_str and (otherSpan.start_index - span.start_index) <= mlparams.MAX_SPAN_MERGE_WORD_SEPARATION:
                    if debug: print("MERGING span: {} ([{}, {}]) => {} ([{}, {}])".format(otherSpan.associated_str, otherSpan.start_index, otherSpan.end_index, span.associated_str, span.start_index, span.end_index))
                    span.end_index = otherSpan.end_index
                    span.size = span.end_index - span.start_index
                    if debug: print("    Span now: {} ([{}, {}])".format(span.associated_str, span.start_index, span.end_index))
                    spansPerTag[tag].remove(otherSpan)
                else:
                    iOtherSpan += 1

            if can_remove_this_tag(json.loads(span.associated_str)): 
                e = mlrules_utils.deserialize_element(span.associated_str)
                if e.tag == mlrules_utils.clean_dict_key_str(mlparams.TAG_USEL)and span.start_index == span.end_index:
                    if debug: print("REMOVING single worded usel span: {} ([{}, {}])".format(span.associated_str, span.start_index, span.end_index))
                    spansPerTag[tag].remove(span)
                    iSpan -= 1

            iSpan += 1

    ###################################################################################
    # End of The Sauce Section ########################################################
    ###################################################################################

    # Check conflicting SCOPEs and remove those spans until none are left
    spansPerTagCheck, conflictSomewhere, (worst_offender_conflict_count, worst_offender_tag, worst_offender_span_index) = check_span_conflicts(spansPerTag)
    while worst_offender_tag != None:
        if debug:
            print("[{}] Removing worst offending tag: '{}': {}".format(worst_offender_conflict_count, worst_offender_tag, worst_offender_span_index))
        spansPerTag = remove_worst_offending_span(spansPerTag, (worst_offender_conflict_count, worst_offender_tag, worst_offender_span_index))
        spansPerTagCheck, conflictSomewhere, (worst_offender_conflict_count, worst_offender_tag, worst_offender_span_index) = check_span_conflicts(spansPerTag)

    # Sort and queue by scope range and start index
    tagsForInsertStagingList = [ ]
    for tag in spansPerTag.keys():
        for span in spansPerTag[tag]:
            tagsForInsertStagingList.append(span)

    tagsForInsertStagingList.sort(key=lambda elem: (elem.start_index, -elem.size)) # Negative to sort second term by reverse order

    if debug:
        print("{}Sorted list of spans (by startindex, then size){}".format(bcolors.PURPLE, bcolors.ENDC))
        for span in tagsForInsertStagingList:
            print("    {}, [{}, {}], len={}".format(span.associated_str, span.start_index, span.end_index, span.size))

    #####################
    # Assemble XML-tree #
    #####################
    result = markup_xmlassembly.spans_to_xml(tagsForInsertStagingList, markedWords, debug_colors=colorizeXmlOutputForEasyDebug)

    if prettyPrint:
        print("{}>>>>>>>>>>>>>>>>>>>> INPUT  <<<<<<<<<<<<<<<<<<<<<{}".format(bcolors.PURPLE, bcolors.ENDC))
        print(origS)
        print("{}>>>>>>>>>>>>>>>>>>>> OUTPUT <<<<<<<<<<<<<<<<<<<<<{}".format(bcolors.PURPLE, bcolors.ENDC))
        print(result)

    # Clean out colorization
    if colorizeXmlOutputForEasyDebug:
        result = result.replace(XML_OUTPUT_COLORIZE_COLOR, '').replace(bcolors.ENDC, '')

    return result
    

def markup(s: str,
           rules: dict,
           markVoice: bool = True,
           markVoiceSpecialMarkGenre: bool = True,
           markBehaviors: bool = True,
           markMoodAndIntensity: Union[Tuple[str, int], None] = None,
           prettyPrint: bool = True,
           markup_pauses: float = None,
           text_replacements: Dict[str, str] = None,
           debug: bool = False) -> str:
    """
    Main function; proceeds as follows:
        - Look up rules per word
        - Create spans of rules based on start/end word indices
            - Merge spans if rules are very close in range
        - Remove conflicting spans that do not nest well
        - Sort
        - Assemble XML tree
        - Output

    Args:
        markup_pauses: pause/break time in seconds between sentences. Defaults None.
    """


    def clean_apostrophe_typos(string: str) -> str:
        """
        Spellcheck issues like "don' t"
        """
        string = " ".join(string.split())
        contractions = ["d", "m", "s", "t", "ve", "re", "ll"]
        return re.sub(fr"' (?=(" + "|".join(contractions) + r")([\s,.;+=\-()\[\]!%]|$))", "'", string)

    def remove_comma_from_large_num(string: str) -> str:
        """
        Ensure generated num remove any commas to prevent unnecessary pauses
        """
        pattern = r'\b(\d+),(\d{3})'
        replacement = r'\1\2'
        string = re.sub(pattern, replacement, string)
        while re.search(pattern, string):
            string = re.sub(pattern, replacement, string)
        return string

    def add_space_between_listed_num(string: str) -> str:
        """
        If Moxie generates a num list, add a space to allow Moxie to pause correctly b/w num
        """
        pattern = r'(\d+),(\d+)[, .]'
        replacement = r'\1, \2, '
        string = re.sub(pattern, replacement, string)
        return string

    def insert_space_between_digits_and_capitals(string: str):
        """
        Use regular expression to find all occurrences of a digit followed immediately by a capital letter
        """
        pattern = r'(\d)([A-Z])'
        replacement = r'\1 \2'
        string = re.sub(pattern, replacement, string)
        return string

    def replace_ellipsis_with_period(text):
        result = text.replace('...', '. ')
        return result 

    def split_to_sentences(text):
        # The regular expression captures the split characters (., !, ?) along with the sentence
        allsentences = re.split(r'(?<=[.!?]) +', text)
        sentences = [sentence.strip() for sentence in allsentences if sentence.strip()]
        return sentences

    def handle_initialisms(text):
        # Look for patterns that start with an optional period, followed by an optional
        # space, a capital letter and a period, capturing the capital letter.
        pattern = r'\b([A-Z])([.!?])'
        result = re.sub(pattern, r'\1 ', text)
        return result
    
    def handle_titles(text):
        titles = {'Mr', 'Mrs', 'Dr', 'Ms', 'Jr', 'Sr'} 
        def replace_match(match):
            word = match.group(1)
            if word in titles:
                return word
            else:
                return match.group(0)
        return re.sub(r'\b(\w+)\.', replace_match, text)

    # Clean any bad characters
    s = clean_apostrophe_typos(s)
    s = remove_comma_from_large_num(s)
    s = add_space_between_listed_num(s)
    s = insert_space_between_digits_and_capitals(s)
    s = handle_initialisms(s)
    s = handle_titles(s)

    # Internal replacement file
    logging.info(f"Replacing text based on internal dictionary ({len(INTERNAL_REPLACE_STRINGS.keys())} keys): {s}")
    for key in INTERNAL_REPLACE_STRINGS.keys():
        # s = replace_string_with_pad(string=s, replace_key=key, replace_value=INTERNAL_REPLACE_STRINGS[key])
        s = s.replace(key, INTERNAL_REPLACE_STRINGS[key])
    logging.info(f"    Replacement result: {s}")

    if sys.version_info < (3, 0):
        s = unidecode(unicode(s, encoding="UTF-8"))
    else:
        s = unidecode(s)

    s = replace_ellipsis_with_period(s)
    sentences = split_to_sentences(s)
    # Add pauses between sentences
    if markup_pauses is None:
        markup_pauses = mlparams.PAUSE_LARGE_TEXT if len(sentences) >= mlparams.LARGE_TEXT_SENTENCE_THRESHOLD \
                                                  else mlparams.PAUSE_DEFAULT
    synthRate = mlparams.SYNTH_RATE_LARGE_TEXT if len(sentences) >= mlparams.LARGE_TEXT_SENTENCE_THRESHOLD \
                                                  else mlparams.SYNTH_RATE_DEFAULT
        
    return  ' '.join(markup_sentence(s = sentence,
                                      rules = rules,
                                      markVoice = markVoice,
                                      synthRate = synthRate,
                                      markVoiceSpecialMarkGenre = markVoiceSpecialMarkGenre,
                                      markBehaviors = markBehaviors,
                                      markMoodAndIntensity = markMoodAndIntensity,
                                      prettyPrint = prettyPrint,
                                      markup_pauses = markup_pauses,
                                      text_replacements = text_replacements,
                                      lastSentence = True if index == len(sentences)-1 else False,
                                      debug = debug) 
                      for index, sentence in enumerate(sentences))
    


