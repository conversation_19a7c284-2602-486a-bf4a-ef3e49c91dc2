# Generated by Django 5.1.3 on 2024-12-31 17:47

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('hive', '0009_alter_moxiedevice_last_connect_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='hiveconfiguration',
            name='common_config',
            field=models.JSONField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='hiveconfiguration',
            name='common_settings',
            field=models.JSONField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='hiveconfiguration',
            name='google_api_key',
            field=models.TextField(blank=True, default='', null=True),
        ),
        migrations.AddField(
            model_name='moxiedevice',
            name='robot_config',
            field=models.J<PERSON><PERSON>ield(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='moxiedevice',
            name='robot_settings',
            field=models.J<PERSON><PERSON><PERSON>(blank=True, null=True),
        ),
        migrations.Add<PERSON>ield(
            model_name='moxiedevice',
            name='state',
            field=models.J<PERSON><PERSON>ield(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='moxiedevice',
            name='state_updated',
            field=models.DateTimeField(blank=True, null=True),
        ),
    ]
