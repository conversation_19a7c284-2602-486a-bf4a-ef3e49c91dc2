# Generated by Django 5.1.3 on 2024-12-26 17:20

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('hive', '0006_moxiedevice_name'),
    ]

    operations = [
        migrations.CreateModel(
            name='MentorBehavior',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('module_id', models.CharField(blank=True, max_length=80, null=True)),
                ('content_id', models.CharField(blank=True, max_length=80, null=True)),
                ('content_day', models.CharField(blank=True, max_length=80, null=True)),
                ('timestamp', models.BigIntegerField()),
                ('action', models.CharField(blank=True, max_length=80, null=True)),
                ('instance_id', models.BigIntegerField()),
                ('ended_reason', models.Char<PERSON><PERSON>(blank=True, max_length=80, null=True)),
                ('device', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='hive.moxiedevice')),
            ],
            options={
                'indexes': [models.Index(fields=['device', 'timestamp'], name='device_timestamp_idx')],
            },
        ),
    ]
