# Generated by Django 5.1.3 on 2025-02-16 00:14

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('hive', '0014_singlepromptchat_code'),
    ]

    operations = [
        migrations.CreateModel(
            name='PersistentData',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('data', models.JSONField()),
                ('device', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='hive.moxiedevice')),
            ],
        ),
    ]
