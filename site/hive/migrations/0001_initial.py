# Generated by Django 5.1.3 on 2024-12-03 01:56

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='MoxieSchedule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('schedule', models.JSONField()),
            ],
        ),
        migrations.CreateModel(
            name='SinglePromptChat',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('module_id', models.CharField(max_length=200)),
                ('content_id', models.CharField(max_length=200)),
                ('max_history', models.IntegerField(default=20)),
                ('max_volleys', models.IntegerField(default=9999)),
                ('opener', models.TextField()),
                ('prompt', models.TextField()),
            ],
        ),
        migrations.CreateModel(
            name='MoxieLogs',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('timestamp', models.TimeField()),
                ('uid', models.IntegerField()),
                ('tag', models.CharField(max_length=80)),
                ('message', models.TextField()),
                ('device', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='hive.moxieschedule')),
            ],
        ),
        migrations.CreateModel(
            name='MoxieDevice',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('device_id', models.CharField(max_length=200)),
                ('email', models.EmailField(max_length=254, null=True)),
                ('permit', models.IntegerField(choices=[(1, 'UNKNOWN'), (2, 'PENDING'), (3, 'ALLOWED')])),
                ('schedule', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='hive.moxieschedule')),
            ],
        ),
    ]
