# Generated by Django 5.1.3 on 2024-12-24 00:34

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('hive', '0004_rename_temperatore_singlepromptchat_temperature'),
    ]

    operations = [
        migrations.CreateModel(
            name='HiveConfiguration',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('openai_api_key', models.TextField(blank=True, default='', null=True)),
                ('external_host', models.CharField(blank=True, default='', max_length=255, null=True)),
                ('allow_unverified_bots', models.BooleanField(default=False)),
            ],
        ),
    ]
