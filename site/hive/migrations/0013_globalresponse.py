# Generated by Django 5.1.3 on 2025-02-04 21:07

import django.core.validators
import re
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('hive', '0012_singlepromptchat_source_version'),
    ]

    operations = [
        migrations.CreateModel(
            name='GlobalResponse',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.TextField()),
                ('pattern', models.TextField()),
                ('entity_groups', models.CharField(blank=True, max_length=255, null=True, validators=[django.core.validators.RegexValidator(re.compile('^\\d+(?:,\\d+)*\\Z'), code='invalid', message='Enter only digits separated by commas.')])),
                ('action', models.IntegerField(choices=[(1, 'RESPONSE'), (2, 'LAUNCH'), (3, 'CONFIRM_LAUNCH'), (4, 'METHOD')], default=1)),
                ('response_text', models.TextField(blank=True, null=True)),
                ('response_markup', models.TextField(blank=True, null=True)),
                ('module_id', models.CharField(blank=True, max_length=80, null=True)),
                ('content_id', models.CharField(blank=True, max_length=80, null=True)),
                ('code', models.TextField(blank=True, null=True)),
                ('sort_key', models.IntegerField(default=1)),
            ],
        ),
    ]
