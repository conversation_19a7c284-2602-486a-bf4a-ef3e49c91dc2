# Generated by Django 5.1.3 on 2024-12-05 01:41

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('hive', '0002_alter_moxiedevice_permit_alter_moxielogs_device'),
    ]

    operations = [
        migrations.AddField(
            model_name='singlepromptchat',
            name='max_tokens',
            field=models.IntegerField(default=70),
        ),
        migrations.AddField(
            model_name='singlepromptchat',
            name='model',
            field=models.CharField(default='gpt-3.5-turbo', max_length=200),
        ),
        migrations.AddField(
            model_name='singlepromptchat',
            name='temperatore',
            field=models.FloatField(default=0.5),
        ),
        migrations.AddField(
            model_name='singlepromptchat',
            name='vendor',
            field=models.IntegerField(choices=[(1, 'OPEN_AI')], default=1),
        ),
        migrations.AlterField(
            model_name='moxiedevice',
            name='email',
            field=models.EmailField(blank=True, max_length=254, null=True),
        ),
    ]
