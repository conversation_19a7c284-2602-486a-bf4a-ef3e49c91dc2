{% extends 'base.html' %}
{% load static %}
{% block content %}
<div class="moxheader"><a href="{% url 'hive:dashboard' %}"><img class="moximage" src="{% static 'hive/openmoxie_logo.svg' %}"></a>OpenMoxie<span class="moxversion">{{moxie_version}}</span></div>
<div class="text-bg-secondary p-3">
<h1>Welcome to OpenMoxie!</h1>
<p>
    The OpenMoxie service allows this computer to act as a replacement for Moxie's cloud infrastructure.
</p>
<div class="half text-bg-danger p-3">
  <p>
    <strong>Please read carefully!</strong> This implementation has minimal security and should always be operated on a LAN behind a firewall that protects
  ports <strong>8883</strong> and <strong>8001</strong> from the Internet.  API keys and service accounts are stored
  unencrypted in the host file system.
  </p>
  <ul>
    <li>Once you add API keys, devices connecting to port <strong>8001</strong> will
  be able to perform chat inferences to OpenAI that will incur charges.</li>
    <li>Devices connected on port <strong>8883</strong> will be able to perform chat inferences and use Speech-to-Text services that will incur charges.</li>
    <li>If you connect a Google Service Account Key, the key itself will be shared with devices connecting on port <strong>8883</strong>.</li>
  </ul>
</div>
<p></p>
<h3>Getting Your Own OpenAI API Key</h3>
<p>
  There are a few requirements to setup before you can use this with your Moxie.  The primary requirement
  is your own personal API key from OpenAI to use for Moxie Chat and the default Speech-to-Text processing.
  These services are not free and you will be responsible for managing your OpenAI account to ensure
  it has sufficient credits, however Whisper is very inexpensive and the default conversational model
  (3.5 turbo) is a very economical model.
</p>
<p>
    You will need to visit <a class="link-light" target="_blank" href="https://platform.openai.com/settings/organization/billing/overview">OpenAI Billing</a> page, 
    create an account, add your credit card, and purchase some credits.  We recommend
    starting with a small amount such as $10 and keeping an eye on your usage until you understand the costs, though they are typically modest.
</p>
<p>
    Next, visit the <a class="link-light" target="_blank" href="https://platform.openai.com/settings/organization/api-keys">OpenAI API Keys</a> to create a unique
    API key (secret key) for use with OpenMoxie.  Give it a nice name like "Moxie Services" and save the API key to paste into the form below.
</p>
<h3>Using Google Speech Instead</h3>
<p>
  OpenMoxie is configured to use OpenAI Whisper for Speech-to-Text by default for simplicity and lower cost, but Google Speech is more
  responsive and generally works better with Moxie's internal speech processing.  Google also offers free credits when you sign-up.
  If you would rather use Google for Speech-to-Text services
  you can include a service account key with permissions to use Google Speech in the form below.  This key will be provided to Moxie and used if you
  set <kbd>"stt": "0"</kbd> in the robot settings.  Visit <a class="link-light" target="_blank" href="https://cloud.google.com/">Google Cloud Platform</a> for details
  on Google's offering.
</p>
<hr>
<h1>Configuration</h1>
<form id="hive_edit" action="{% url 'hive:hive_configure' %}" method="post">
    {% csrf_token %}
    <div class="mb-3">
      <label for="apikey" class="form-label fw-bold fs-5">OpenAI API Key</label>
      <input type="text" class="form-control" name="apikey" id="apikey" aria-describedby="apiHelp" placeholder="{% if object.openai_api_key %}[Key exists and will be kept unless overridden.]{% else %}Paste OpenAI API key.{% endif %}">
      <div id="apiHelp" class="form-text text-light">This be be stored in your local database and used locally for AI and Speech services.</div>
    </div>
    <div class="mb-3">
      <label for="googleapikey" class="form-label fw-bold fs-5">(OPTIONAL) Google Service Account Key</label>
      <input type="text" class="form-control" name="googleapikey" id="googleapikey" aria-describedby="googleapiHelp" placeholder="{% if object.google_api_key %}[Key exists and will be kept unless overridden.]{% else %}Paste Google Service Account key.{% endif %}">
      <div id="googleapiHelp" class="form-text text-light">
        This be be stored in your local database and provided to connecting Moxie.
      </div>
    </div>
    <div class="mb-3">
      <label for="hostname" class="form-label fw-bold fs-5">Hostname</label>
      <div class="half text-bg-warning p-3" style="margin-bottom: 5px;">
        <strong>Important! </strong>Hostname is the address Moxie will use to talk to your PC.  It is best to use your computer name, 
        but if your router does not provide LAN DNS services, you may need to use your LAN IP address here.
      </div>
      <input type="text" class="form-control" name="hostname" id="hostname" aria-describedby="hostnameHelp" placeholder="Enter computer name" value="{{object.external_host}}">
      <div id="hostnameHelp" class="form-text text-light">The local network name of this computer, either your computer name or LAN network address.</div>
    </div>
    {% if needs_admin %}
    <h3>Database Administrator</h3>
    <p>Setup the local database administrator password.  This is the superuser password for your local SQLite database and is used when accessing the Admin page for this project.</p>
    <div class="half text-bg-warning p-3" style="margin-bottom: 5px;">
      <strong>Important! </strong>You will need this password to access the Admin section later!
    </div>
    <div class="row mb-3">
      <label for="adminUser" class="col-sm-2 col-form-label">Admin User</label>
      <div class="col-sm-10">
        <input type="text" class="form-control" id="adminUser" name="adminUser" value="admin">
      </div>
    </div>
    <div class="row mb-3">
      <label for="adminPassword" class="col-sm-2 col-form-label">Admin Password</label>
      <div class="col-sm-10">
        <input type="password" class="form-control" id="adminPassword" name="adminPassword">
      </div>
    </div>
    {% endif %}
<!--
    <div class="mb-3 form-check">
      <input type="checkbox" class="form-check-input" name="allowall" id="allowall" aria-describedby="allowHelp">
      <label class="form-check-label" for="allowall">Allow Services to Any Moxie</label>
      <div id="hostnameHelp" class="form-text text-light">Check this to allow use by any Moxie on your network, otherwise each will need to be authorized explicity.</div>
    </div>
 -->
    <button type="submit" class="btn btn-primary">Submit</button>
  </form>
</div>
<script>
$(document).ready(function() {
  $('#hive_edit').submit(function(event) {
    var validated = true;
    var err_msg = "Setup Cannot Complete:";

    var hostname = $('#hostname').val().trim();
    var googleApiKey = $('#googleapikey').val().trim();
    {% if needs_admin %}
    var adminUser = $('#adminUser').val().trim();
    var adminPassword = $('#adminPassword').val().trim();
    if (adminUser === '') {
      err_msg += '\n-Admin User cannot be empty';
      validated = false;
    }
    if (adminPassword === '') {
      err_msg += '\n-Admin Password cannot be empty';
      validated = false;
    }
    {% endif %}
    if (hostname === '') {
      err_msg += '\n-Hostname cannot be empty';
      validated = false;
    }
    if (googleApiKey !== '' && !isValidJSON(googleApiKey)) {
      err_msg += '\n-Google API Key must be a valid JSON string';
      validated = false;
    }

    if (!validated) {
      alert(err_msg);
      event.preventDefault();
    }
  });
});

function isValidJSON(jsonString) {
  try {
    JSON.parse(jsonString);
  } catch (e) {
    return false;
  }
  return true;
}
</script>
{% endblock %}