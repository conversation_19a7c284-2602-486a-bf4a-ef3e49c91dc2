# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: embodied/wifiapp/QRCommands.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from embodied.logging import enums_pb2 as embodied_dot_logging_dot_enums__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n!embodied/wifiapp/QRCommands.proto\x12\x0e\x65mbodied.unity\x1a\x1c\x65mbodied/logging/enums.proto\"\xac\x01\n\tQRCommand\x12\x11\n\ttimestamp\x18\x01 \x01(\x04\x12\x0c\n\x04\x63ode\x18\x02 \x01(\t\x12\r\n\x05param\x18\x03 \x01(\t\x12/\n\x08\x65ndpoint\x18\x04 \x01(\x0e\x32\x1d.embodied.logging.IOTEndpoint\x12\x0f\n\x07\x63ommand\x18\x05 \x01(\t\x12\x18\n\x10software_version\x18\x64 \x01(\t\x12\x13\n\x0bmodule_name\x18\x65 \x01(\t\"w\n\nQRResponse\x12\x11\n\ttimestamp\x18\x01 \x01(\x04\x12\x15\n\rresponse_code\x18\x02 \x01(\r\x12\x10\n\x08response\x18\x03 \x01(\t\x12\x18\n\x10software_version\x18\x64 \x01(\t\x12\x13\n\x0bmodule_name\x18\x65 \x01(\t\"\xaa\x01\n\x10QRDiagnosticData\x12\x12\n\nrobot_uuid\x18\x01 \x01(\t\x12\x0f\n\x07rsa_pub\x18\x02 \x01(\t\x12\x17\n\x0f\x63loud_connected\x18\x03 \x01(\x08\x12\x12\n\nuser_state\x18\x04 \x01(\r\x12\x15\n\rcloud_project\x18\x05 \x01(\t\x12\x18\n\x10software_version\x18\x64 \x01(\t\x12\x13\n\x0bmodule_name\x18\x65 \x01(\t\"\xaa\x02\n\x0eStartPairingQR\x12\x0c\n\x04ssid\x18\x01 \x01(\t\x12\x10\n\x08password\x18\x02 \x01(\t\x12\x12\n\nis_staging\x18\x03 \x01(\x08\x12\x12\n\nsecret_key\x18\x04 \x01(\x0c\x12\x11\n\twifi_only\x18\x05 \x01(\x08\x12\x11\n\tis_hidden\x18\x06 \x01(\x08\x12\x42\n\x0b\x62\x61nd_select\x18\x07 \x01(\x0e\x32-.embodied.unity.StartPairingQR.WifiBandSelect\x12/\n\x08\x65ndpoint\x18\x08 \x01(\x0e\x32\x1d.embodied.logging.IOTEndpoint\"5\n\x0eWifiBandSelect\x12\x07\n\x03\x41NY\x10\x00\x12\x0c\n\x08ONLY_50G\x10\x01\x12\x0c\n\x08ONLY_24G\x10\x02\"\x9a\x01\n\x11WifiNetworkUpdate\x12\x11\n\ttimestamp\x18\x01 \x01(\x04\x12\x31\n\twifi_info\x18\x02 \x01(\x0b\x32\x1e.embodied.unity.StartPairingQR\x12\x10\n\x08\x61\x64\x64_only\x18\x03 \x01(\x08\x12\x18\n\x10software_version\x18\x64 \x01(\t\x12\x13\n\x0bmodule_name\x18\x65 \x01(\t\"Q\n\x0eQRMultiDecoder\x12(\n\x05\x64\x65\x62ug\x18\x01 \x01(\x0b\x32\x19.embodied.unity.QRCommand\x12\x15\n\rencoded_proto\x18\x02 \x01(\x0c\"\xdf\x02\n\x0bQRVPNConfig\x12\x11\n\ttimestamp\x18\x01 \x01(\x04\x12\x37\n\x07\x63ommand\x18\x02 \x01(\x0e\x32&.embodied.unity.QRVPNConfig.VPNCommand\x12\x0e\n\x06vpn_id\x18\x03 \x01(\t\x12\x0b\n\x03url\x18\x04 \x01(\t\x12\x10\n\x08username\x18\x05 \x01(\t\x12\x10\n\x08password\x18\x06 \x01(\t\x12\x0f\n\x07\x63onnect\x18\x07 \x01(\x08\x12\x18\n\x10software_version\x18\x64 \x01(\t\x12\x13\n\x0bmodule_name\x18\x65 \x01(\t\"\x82\x01\n\nVPNCommand\x12\x17\n\x13UNKNOWN_VPN_COMMAND\x10\x00\x12\x10\n\x0cVPN_DOWNLOAD\x10\x01\x12\x0e\n\nVPN_REVERT\x10\x02\x12\x13\n\x0fVPN_CREDENTIALS\x10\x03\x12\x10\n\x0cVPN_ACTIVATE\x10\x04\x12\x12\n\x0eVPN_DEACTIVATE\x10\x05\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'embodied.wifiapp.QRCommands_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _globals['_QRCOMMAND']._serialized_start=84
  _globals['_QRCOMMAND']._serialized_end=256
  _globals['_QRRESPONSE']._serialized_start=258
  _globals['_QRRESPONSE']._serialized_end=377
  _globals['_QRDIAGNOSTICDATA']._serialized_start=380
  _globals['_QRDIAGNOSTICDATA']._serialized_end=550
  _globals['_STARTPAIRINGQR']._serialized_start=553
  _globals['_STARTPAIRINGQR']._serialized_end=851
  _globals['_STARTPAIRINGQR_WIFIBANDSELECT']._serialized_start=798
  _globals['_STARTPAIRINGQR_WIFIBANDSELECT']._serialized_end=851
  _globals['_WIFINETWORKUPDATE']._serialized_start=854
  _globals['_WIFINETWORKUPDATE']._serialized_end=1008
  _globals['_QRMULTIDECODER']._serialized_start=1010
  _globals['_QRMULTIDECODER']._serialized_end=1091
  _globals['_QRVPNCONFIG']._serialized_start=1094
  _globals['_QRVPNCONFIG']._serialized_end=1445
  _globals['_QRVPNCONFIG_VPNCOMMAND']._serialized_start=1315
  _globals['_QRVPNCONFIG_VPNCOMMAND']._serialized_end=1445
# @@protoc_insertion_point(module_scope)
