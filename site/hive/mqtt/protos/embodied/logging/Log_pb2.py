# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: embodied/logging/Log.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1a\x65mbodied/logging/Log.proto\x12\x10\x65mbodied.logging\"~\n\tLogDevice\x12\x1c\n\x14timestamp_deprecated\x18\x01 \x01(\x01\x12\x12\n\ndeviceUUID\x18\x02 \x01(\t\x12\x19\n\x11\x65ventArgsTypename\x18\x03 \x01(\t\x12\x11\n\teventArgs\x18\x04 \x01(\x0c\x12\x11\n\ttimestamp\x18\x05 \x01(\x04\"\x8e\x01\n\x07LogUser\x12\x1c\n\x14timestamp_deprecated\x18\x01 \x01(\x01\x12\x12\n\ndeviceUUID\x18\x02 \x01(\t\x12\x10\n\x08userUUID\x18\x03 \x01(\t\x12\x19\n\x11\x65ventArgsTypename\x18\x04 \x01(\t\x12\x11\n\teventArgs\x18\x05 \x01(\x0c\x12\x11\n\ttimestamp\x18\x06 \x01(\x04\"w\n\x0bLogcatTrace\x12\x11\n\ttimestamp\x18\x01 \x01(\t\x12\r\n\x05level\x18\x02 \x01(\t\x12\x0b\n\x03tag\x18\x03 \x01(\t\x12\x0b\n\x03pid\x18\x04 \x01(\r\x12\x0f\n\x07message\x18\x05 \x01(\t\x12\x0b\n\x03tid\x18\x06 \x01(\r\x12\x0e\n\x06\x62o_uid\x18\x07 \x01(\r\"z\n\x0e\x44\x65viceSettings\x12:\n\x05props\x18\x01 \x03(\x0b\x32+.embodied.logging.DeviceSettings.PropsEntry\x1a,\n\nPropsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"X\n\x14\x44\x65viceSettingsUpdate\x12\x11\n\ttimestamp\x18\x01 \x01(\x04\x12\x18\n\x10software_version\x18\x64 \x01(\t\x12\x13\n\x0bmodule_name\x18\x65 \x01(\t\"b\n\x0eProtoSubscribe\x12\x11\n\ttimestamp\x18\x01 \x01(\x04\x12\x0e\n\x06protos\x18\x02 \x03(\t\x12\x18\n\x10software_version\x18\x64 \x01(\t\x12\x13\n\x0bmodule_name\x18\x65 \x01(\t\"p\n\x04Ping\x12\x11\n\ttimestamp\x18\x01 \x01(\x04\x12\x13\n\x0binclude_zmq\x18\x02 \x01(\x08\x12\x11\n\tuser_data\x18\x03 \x01(\t\x12\x18\n\x10software_version\x18\x64 \x01(\t\x12\x13\n\x0bmodule_name\x18\x65 \x01(\tb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'embodied.logging.Log_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _DEVICESETTINGS_PROPSENTRY._options = None
  _DEVICESETTINGS_PROPSENTRY._serialized_options = b'8\001'
  _globals['_LOGDEVICE']._serialized_start=48
  _globals['_LOGDEVICE']._serialized_end=174
  _globals['_LOGUSER']._serialized_start=177
  _globals['_LOGUSER']._serialized_end=319
  _globals['_LOGCATTRACE']._serialized_start=321
  _globals['_LOGCATTRACE']._serialized_end=440
  _globals['_DEVICESETTINGS']._serialized_start=442
  _globals['_DEVICESETTINGS']._serialized_end=564
  _globals['_DEVICESETTINGS_PROPSENTRY']._serialized_start=520
  _globals['_DEVICESETTINGS_PROPSENTRY']._serialized_end=564
  _globals['_DEVICESETTINGSUPDATE']._serialized_start=566
  _globals['_DEVICESETTINGSUPDATE']._serialized_end=654
  _globals['_PROTOSUBSCRIBE']._serialized_start=656
  _globals['_PROTOSUBSCRIBE']._serialized_end=754
  _globals['_PING']._serialized_start=756
  _globals['_PING']._serialized_end=868
# @@protoc_insertion_point(module_scope)
