# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: embodied/logging/Cloud2.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from embodied.logging import enums_pb2 as embodied_dot_logging_dot_enums__pb2
from embodied.logging import Log_pb2 as embodied_dot_logging_dot_Log__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1d\x65mbodied/logging/Cloud2.proto\x12\x10\x65mbodied.logging\x1a\x1c\x65mbodied/logging/enums.proto\x1a\x1a\x65mbodied/logging/Log.proto\"\xf2\x03\n\x15ServiceConfiguration2\x12\x13\n\x0bgcp_project\x18\x01 \x01(\t\x12\x17\n\x0fwebservice_root\x18\x02 \x01(\t\x12\x16\n\x0ewebservice_pin\x18\x03 \x01(\t\x12\x14\n\x0c\x64isable_sync\x18\x04 \x01(\x08\x12\x1a\n\x12\x64isable_log_upload\x18\x05 \x01(\x08\x12\x10\n\x08\x65ndpoint\x18\x06 \x01(\t\x12\x11\n\ttimestamp\x18\x07 \x01(\x04\x12\x11\n\tmqtt_host\x18\x08 \x01(\t\x12O\n\x0f\x63onnection_type\x18\t \x01(\x0e\x32\x36.embodied.logging.ServiceConfiguration2.ConnectionType\x12\x32\n\x0b\x65ndpoint_id\x18\n \x01(\x0e\x32\x1d.embodied.logging.IOTEndpoint\x12\x15\n\roverride_port\x18\x0b \x01(\r\x12\x16\n\x0e\x64isable_verify\x18\x0c \x01(\x08\x12\x18\n\x10software_version\x18\x64 \x01(\t\x12\x13\n\x0bmodule_name\x18\x65 \x01(\t\"F\n\x0e\x43onnectionType\x12\x0e\n\nGOOGLE_IOT\x10\x00\x12\x10\n\x0c\x45MBODIED_IOT\x10\x01\x12\x12\n\x0e\x45MBODIED_LOCAL\x10\x02\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'embodied.logging.Cloud2_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _globals['_SERVICECONFIGURATION2']._serialized_start=110
  _globals['_SERVICECONFIGURATION2']._serialized_end=608
  _globals['_SERVICECONFIGURATION2_CONNECTIONTYPE']._serialized_start=538
  _globals['_SERVICECONFIGURATION2_CONNECTIONTYPE']._serialized_end=608
# @@protoc_insertion_point(module_scope)
