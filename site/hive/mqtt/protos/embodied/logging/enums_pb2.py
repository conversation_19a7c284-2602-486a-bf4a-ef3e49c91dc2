# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: embodied/logging/enums.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1c\x65mbodied/logging/enums.proto\x12\x10\x65mbodied.logging*=\n\x0cLoggingState\x12\t\n\x05START\x10\x00\x12\x0b\n\x07STARTED\x10\x01\x12\x08\n\x04STOP\x10\x02\x12\x0b\n\x07STOPPED\x10\x03*4\n\rLoggingPolicy\x12\x0b\n\x07NO_DATA\x10\x00\x12\x0c\n\x08NO_MEDIA\x10\x01\x12\x08\n\x04\x46ULL\x10\x02*\xff\x01\n\x0bIOTEndpoint\x12\x0f\n\x0bIOT_DEFAULT\x10\x00\x12\x12\n\x0eGOOGLE_DEVELOP\x10\x01\x12\x12\n\x0eGOOGLE_STAGING\x10\x02\x12\x15\n\x11GOOGLE_PRODUCTION\x10\x03\x12\x14\n\x10\x45MBODIED_DEVELOP\x10\x04\x12\x14\n\x10\x45MBODIED_STAGING\x10\x05\x12\x17\n\x13\x45MBODIED_PRODUCTION\x10\x06\x12\x12\n\x0e\x45MBODIED_HIPAA\x10\x07\x12\x12\n\x0e\x45MBODIED_LOCAL\x10\x08\x12\x12\n\x0e\x45MBODIED_CHINA\x10\t\x12\x0f\n\x0b\x45MBODIED_HK\x10\n\x12\x0e\n\nOPEN_MOXIE\x10\x0b\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'embodied.logging.enums_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _globals['_LOGGINGSTATE']._serialized_start=50
  _globals['_LOGGINGSTATE']._serialized_end=111
  _globals['_LOGGINGPOLICY']._serialized_start=113
  _globals['_LOGGINGPOLICY']._serialized_end=165
  _globals['_IOTENDPOINT']._serialized_start=168
  _globals['_IOTENDPOINT']._serialized_end=423
# @@protoc_insertion_point(module_scope)
