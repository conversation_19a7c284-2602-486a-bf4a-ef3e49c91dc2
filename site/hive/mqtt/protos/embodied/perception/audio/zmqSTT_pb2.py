# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: embodied/perception/audio/zmqSTT.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n&embodied/perception/audio/zmqSTT.proto\x12\x19\x65mbodied.perception.audio\"\x83\x02\n\rzmqSTTRequest\x12\x11\n\ttimestamp\x18\x01 \x01(\x04\x12>\n\x03vad\x18\x02 \x01(\x0e\x32\x31.embodied.perception.audio.zmqSTTRequest.VADState\x12\x15\n\raudio_content\x18\x03 \x01(\x0c\x12\x0c\n\x04uuid\x18\x04 \x01(\t\x12\x18\n\x10software_version\x18\x64 \x01(\t\x12\x13\n\x0bmodule_name\x18\x65 \x01(\t\"K\n\x08VADState\x12\x0b\n\x07UNKNOWN\x10\x00\x12\x13\n\x0fSTART_OF_SPEECH\x10\x01\x12\n\n\x06SPEECH\x10\x02\x12\x11\n\rEND_OF_SPEECH\x10\x03\"\xdc\x03\n\x0ezmqSTTResponse\x12\x11\n\ttimestamp\x18\x01 \x01(\x04\x12\x44\n\x04type\x18\x02 \x01(\x0e\x32\x36.embodied.perception.audio.zmqSTTResponse.ResponseType\x12\x0e\n\x06speech\x18\x03 \x01(\t\x12\x12\n\nconfidence\x18\x04 \x01(\x02\x12\x15\n\rend_timestamp\x18\x05 \x01(\x04\x12\x17\n\x0fstart_timestamp\x18\x06 \x01(\x04\x12\x0c\n\x04uuid\x18\x07 \x01(\t\x12\x12\n\nerror_code\x18\x08 \x01(\r\x12\x15\n\rerror_message\x18\t \x01(\t\x12\x10\n\x08language\x18\n \x01(\t\x12\x14\n\x0c\x61lternatives\x18\x0b \x03(\t\x12\x19\n\x11original_language\x18\x0c \x01(\t\x12\x17\n\x0foriginal_speech\x18\r \x01(\t\x12\x1d\n\x15original_alternatives\x18\x0e \x03(\t\x12\x12\n\nspeaker_id\x18\x0f \x03(\x02\x12\x18\n\x10software_version\x18\x64 \x01(\t\x12\x13\n\x0bmodule_name\x18\x65 \x01(\t\"&\n\x0cResponseType\x12\x0b\n\x07PARTIAL\x10\x00\x12\t\n\x05\x46INAL\x10\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'embodied.perception.audio.zmqSTT_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _globals['_ZMQSTTREQUEST']._serialized_start=70
  _globals['_ZMQSTTREQUEST']._serialized_end=329
  _globals['_ZMQSTTREQUEST_VADSTATE']._serialized_start=254
  _globals['_ZMQSTTREQUEST_VADSTATE']._serialized_end=329
  _globals['_ZMQSTTRESPONSE']._serialized_start=332
  _globals['_ZMQSTTRESPONSE']._serialized_end=808
  _globals['_ZMQSTTRESPONSE_RESPONSETYPE']._serialized_start=770
  _globals['_ZMQSTTRESPONSE_RESPONSETYPE']._serialized_end=808
# @@protoc_insertion_point(module_scope)
